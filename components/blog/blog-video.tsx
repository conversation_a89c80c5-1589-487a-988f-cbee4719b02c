'use client';

import { useState } from 'react';
import { Play, ExternalLink } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BlogVideoProps {
  src: string;
  title?: string;
  caption?: string;
  width?: number;
  height?: number;
  className?: string;
  autoplay?: boolean;
  muted?: boolean;
}

// 从YouTube URL提取视频ID的函数
function extractYouTubeId(url: string): string | null {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/shorts\/)([^&\n?#]+)/,
    /youtube\.com\/v\/([^&\n?#]+)/,
  ];
  
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      return match[1];
    }
  }
  
  return null;
}

// 检查是否为YouTube URL
function isYouTubeUrl(url: string): boolean {
  return url.includes('youtube.com') || url.includes('youtu.be');
}

export function BlogVideo({ 
  src, 
  title = 'Video', 
  caption, 
  width = 560, 
  height = 315,
  className,
  autoplay = false,
  muted = false
}: BlogVideoProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  // 处理YouTube视频
  if (isYouTubeUrl(src)) {
    const videoId = extractYouTubeId(src);
    
    if (!videoId) {
      return (
        <div className={cn('my-6 p-4 border border-red-200 rounded-lg bg-red-50', className)}>
          <p className="text-red-600 text-sm">无效的YouTube视频链接</p>
        </div>
      );
    }

    // YouTube Shorts 特殊处理
    const isShorts = src.includes('/shorts/');
    const embedUrl = `https://www.youtube.com/embed/${videoId}${autoplay ? '?autoplay=1' : ''}${muted ? (autoplay ? '&muted=1' : '?muted=1') : ''}`;
    
    return (
      <figure className={cn('my-6', className)}>
        <div className="relative overflow-hidden rounded-lg bg-muted">
          {!isLoaded && (
            <div 
              className="absolute inset-0 flex items-center justify-center bg-gray-100 cursor-pointer"
              onClick={() => setIsLoaded(true)}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-red-600 rounded-full flex items-center justify-center">
                  <Play className="w-8 h-8 text-white ml-1" />
                </div>
                <p className="text-sm text-gray-600 mb-2">点击播放YouTube视频</p>
                <p className="text-xs text-gray-500">{title}</p>
              </div>
            </div>
          )}
          
          {isLoaded && (
            <iframe
              width={width}
              height={isShorts ? Math.min(height * 1.5, 500) : height} // Shorts使用更高的比例
              src={embedUrl}
              title={title}
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
              className="w-full"
              onError={() => setHasError(true)}
            />
          )}
          
          {hasError && (
            <div className="p-4 text-center">
              <p className="text-red-600 text-sm mb-2">视频加载失败</p>
              <a 
                href={src} 
                target="_blank" 
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 text-sm"
              >
                在YouTube上观看 <ExternalLink className="w-4 h-4" />
              </a>
            </div>
          )}
        </div>
        
        {caption && (
          <figcaption className="text-sm text-muted-foreground text-center mt-2 italic">
            {caption}
          </figcaption>
        )}
      </figure>
    );
  }

  // 处理普通视频文件
  return (
    <figure className={cn('my-6', className)}>
      <div className="relative overflow-hidden rounded-lg bg-muted">
        <video
          width={width}
          height={height}
          controls
          className="w-full h-auto"
          preload="metadata"
          onError={() => setHasError(true)}
        >
          <source src={src} type="video/mp4" />
          <source src={src} type="video/webm" />
          <source src={src} type="video/ogg" />
          您的浏览器不支持视频播放。
        </video>
        
        {hasError && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center">
              <p className="text-red-600 text-sm mb-2">视频加载失败</p>
              <a 
                href={src} 
                target="_blank" 
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 text-sm"
              >
                直接访问视频 <ExternalLink className="w-4 h-4" />
              </a>
            </div>
          </div>
        )}
      </div>
      
      {caption && (
        <figcaption className="text-sm text-muted-foreground text-center mt-2 italic">
          {caption}
        </figcaption>
      )}
    </figure>
  );
}
