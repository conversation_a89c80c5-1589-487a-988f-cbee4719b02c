import type { MDXComponents } from "mdx/types";
import { BlogImage } from '@/components/blog/blog-image';
import { BlogVideo } from '@/components/blog/blog-video';
import { CalloutBox } from '@/components/blog/callout-box';
import { CodeBlock } from '@/components/blog/code-block';

export function useMDXComponents(components: MDXComponents): MDXComponents {
  return {
    // 自定义组件
    BlogImage,
    BlogVideo,
    CalloutBox,
    CodeBlock,

    // 重写默认 HTML 元素
    img: (props: any) => (
      <BlogImage
        src={props.src}
        alt={props.alt || ''}
        caption={props.title}
        {...props}
      />
    ),

    pre: (props: any) => (
      <CodeBlock {...props} />
    ),

    // 自定义标题样式
    h1: (props: any) => (
      <h1 className="text-3xl font-bold tracking-tight mt-8 mb-4 first:mt-0" {...props} />
    ),
    h2: (props: any) => (
      <h2 className="text-2xl font-semibold tracking-tight mt-8 mb-4 first:mt-0" {...props} />
    ),
    h3: (props: any) => (
      <h3 className="text-xl font-semibold tracking-tight mt-6 mb-3 first:mt-0" {...props} />
    ),
    h4: (props: any) => (
      <h4 className="text-lg font-semibold tracking-tight mt-6 mb-3 first:mt-0" {...props} />
    ),

    // 自定义段落样式
    p: (props: any) => (
      <p className="text-base leading-7 mb-4" {...props} />
    ),

    // 自定义列表样式
    ul: (props: any) => (
      <ul className="list-disc list-inside space-y-2 mb-4 ml-4" {...props} />
    ),
    ol: (props: any) => (
      <ol className="list-decimal list-inside space-y-2 mb-4 ml-4" {...props} />
    ),
    li: (props: any) => (
      <li className="text-base leading-7" {...props} />
    ),

    // 自定义引用样式
    blockquote: (props: any) => (
      <blockquote className="border-l-4 border-primary pl-4 py-2 my-6 bg-muted/50 rounded-r-lg italic" {...props} />
    ),

    // 自定义链接样式
    a: (props: any) => (
      <a
        className="text-primary hover:text-primary/80 underline underline-offset-4 transition-colors"
        target={props.href?.startsWith('http') ? '_blank' : undefined}
        rel={props.href?.startsWith('http') ? 'noopener noreferrer' : undefined}
        {...props}
      />
    ),

    // 自定义代码样式
    code: (props: any) => (
      <code className="bg-muted px-1.5 py-0.5 rounded text-sm font-mono" {...props} />
    ),

    // 自定义分隔线
    hr: (props: any) => (
      <hr className="my-8 border-border" {...props} />
    ),

    ...components,
  };
}
